import { Request, Response } from 'express';
import { db } from '../db';
import { stoerungen } from '../db/schema';

// Hilfsfunktion zum sicheren Pa<PERSON> von <PERSON>
function safeJsonParse(jsonString: string | null): any[] {
  if (!jsonString) return [];
  try {
    const parsed = JSON.parse(jsonString);
    return Array.isArray(parsed) ? parsed : [];
  } catch (e) {
    console.error('[RunbookController] <PERSON><PERSON> beim <PERSON> von <PERSON>', e);
    return [];
  }
}

export class RunbookController {
  /**
   * GET /api/runbooks
   * Holt alle Runbooks
   */
  async getAllRunbooks(req: Request, res: Response): Promise<void> {
    try {
      const runbooks = await prisma.runbook.findMany({
        orderBy: {
          updated_at: 'desc'
        }
      });

      const formattedRunbooks = runbooks.map(runbook => ({
        id: runbook.id,
        title: runbook.title,
        content: runbook.content,
        affected_systems: safeJsonParse(runbook.affected_systems),
        tags: safeJsonParse(runbook.tags),
        created_at: runbook.created_at.toISOString(),
        updated_at: runbook.updated_at.toISOString()
      }));

      res.json({
        success: true,
        data: formattedRunbooks
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Laden der Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden der Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/runbooks/:id
   * Holt ein spezifisches Runbook
   */
  async getRunbookById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const runbookId = parseInt(id, 10);

      if (isNaN(runbookId)) {
        res.status(400).json({
          success: false,
          error: 'Ungültige Runbook-ID'
        });
        return;
      }

      const runbook = await prisma.runbook.findUnique({
        where: { id: runbookId }
      });

      if (!runbook) {
        res.status(404).json({
          success: false,
          error: 'Runbook nicht gefunden'
        });
        return;
      }

      const formattedRunbook = {
        id: runbook.id,
        title: runbook.title,
        content: runbook.content,
        affected_systems: safeJsonParse(runbook.affected_systems),
        tags: safeJsonParse(runbook.tags),
        created_at: runbook.created_at.toISOString(),
        updated_at: runbook.updated_at.toISOString()
      };

      res.json({
        success: true,
        data: formattedRunbook
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Laden des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Laden des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * POST /api/runbooks
   * Erstellt ein neues Runbook
   */
  async createRunbook(req: Request, res: Response): Promise<void> {
    try {
      const { title, content, affected_systems, tags } = req.body;

      if (!title || !content) {
        res.status(400).json({
          success: false,
          error: 'Titel und Inhalt sind erforderlich'
        });
        return;
      }

      const runbook = await prisma.runbook.create({
        data: {
          title: title.trim(),
          content: content.trim(),
          affected_systems: affected_systems ? JSON.stringify(affected_systems) : null,
          tags: tags ? JSON.stringify(tags) : null
        }
      });

      const formattedRunbook = {
        id: runbook.id,
        title: runbook.title,
        content: runbook.content,
        affected_systems: safeJsonParse(runbook.affected_systems),
        tags: safeJsonParse(runbook.tags),
        created_at: runbook.created_at.toISOString(),
        updated_at: runbook.updated_at.toISOString()
      };

      res.status(201).json({
        success: true,
        data: formattedRunbook
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Erstellen des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Erstellen des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * PUT /api/runbooks/:id
   * Aktualisiert ein bestehendes Runbook
   */
  async updateRunbook(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const runbookId = parseInt(id, 10);
      const { title, content, affected_systems, tags } = req.body;

      if (isNaN(runbookId)) {
        res.status(400).json({
          success: false,
          error: 'Ungültige Runbook-ID'
        });
        return;
      }

      const existingRunbook = await prisma.runbook.findUnique({
        where: { id: runbookId }
      });

      if (!existingRunbook) {
        res.status(404).json({
          success: false,
          error: 'Runbook nicht gefunden'
        });
        return;
      }

      const runbook = await prisma.runbook.update({
        where: { id: runbookId },
        data: {
          title: title?.trim() || existingRunbook.title,
          content: content?.trim() || existingRunbook.content,
          affected_systems: affected_systems ? JSON.stringify(affected_systems) : existingRunbook.affected_systems,
          tags: tags ? JSON.stringify(tags) : existingRunbook.tags
        }
      });

      const formattedRunbook = {
        id: runbook.id,
        title: runbook.title,
        content: runbook.content,
        affected_systems: safeJsonParse(runbook.affected_systems),
        tags: safeJsonParse(runbook.tags),
        created_at: runbook.created_at.toISOString(),
        updated_at: runbook.updated_at.toISOString()
      };

      res.json({
        success: true,
        data: formattedRunbook
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Aktualisieren des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Aktualisieren des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * DELETE /api/runbooks/:id
   * Löscht ein Runbook
   */
  async deleteRunbook(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const runbookId = parseInt(id, 10);

      if (isNaN(runbookId)) {
        res.status(400).json({
          success: false,
          error: 'Ungültige Runbook-ID'
        });
        return;
      }

      const existingRunbook = await prisma.runbook.findUnique({
        where: { id: runbookId }
      });

      if (!existingRunbook) {
        res.status(404).json({
          success: false,
          error: 'Runbook nicht gefunden'
        });
        return;
      }

      await prisma.runbook.delete({
        where: { id: runbookId }
      });

      res.json({
        success: true,
        message: 'Runbook erfolgreich gelöscht'
      });
    } catch (error) {
      console.error('[RunbookController] Fehler beim Löschen des Runbooks:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler beim Löschen des Runbooks',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }

  /**
   * GET /api/runbooks/search
   * Sucht nach Runbooks basierend auf Query-Parameter
   */
  async searchRunbooks(req: Request, res: Response): Promise<void> {
    try {
      const { q } = req.query;
      
      if (!q || typeof q !== 'string') {
        res.status(400).json({
          success: false,
          error: 'Suchbegriff ist erforderlich'
        });
        return;
      }

      const searchTerm = q.trim().toLowerCase();

      const runbooks = await prisma.runbook.findMany({
        where: {
          OR: [
            {
              title: {
                contains: searchTerm
              }
            },
            {
              content: {
                contains: searchTerm
              }
            },
            {
              tags: {
                contains: searchTerm
              }
            }
          ]
        },
        orderBy: {
          updated_at: 'desc'
        }
      });

      const formattedRunbooks = runbooks.map(runbook => ({
        id: runbook.id,
        title: runbook.title,
        content: runbook.content,
        affected_systems: safeJsonParse(runbook.affected_systems),
        tags: safeJsonParse(runbook.tags),
        created_at: runbook.created_at.toISOString(),
        updated_at: runbook.updated_at.toISOString()
      }));

      res.json({
        success: true,
        data: formattedRunbooks,
        count: formattedRunbooks.length
      });
    } catch (error) {
      console.error('[RunbookController] Fehler bei der Runbook-Suche:', error);
      res.status(500).json({
        success: false,
        error: 'Fehler bei der Runbook-Suche',
        message: error instanceof Error ? error.message : 'Unbekannter Fehler'
      });
    }
  }
}

export default RunbookController;