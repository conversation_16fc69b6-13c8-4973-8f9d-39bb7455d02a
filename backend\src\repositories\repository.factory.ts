/**
 * Repository Factory
 * 
 * Zentrale Factory-Klasse für die Erstellung und Verwaltung
 * aller Repository-Instanzen mit Singleton-Pattern.
 */

import { db } from '../db';
import { RepositoryFactory, DispatchRepository, WarehouseRepository, CuttingRepository } from './interfaces';
import { DispatchRepositoryImpl } from './dispatch.repository';
import { WarehouseRepositoryImpl } from './warehouse.repository';
import { CuttingRepositoryImpl } from './cutting.repository';

/**
 * Repository Factory Implementation
 */
export class RepositoryFactoryImpl implements RepositoryFactory {
  private static instance: RepositoryFactoryImpl;
  private db = db;
  
  // Repository Singletons
  private _dispatchRepository?: DispatchRepository;
  private _warehouseRepository?: WarehouseRepository;
  private _cuttingRepository?: CuttingRepository;

  private constructor() {
    // Drizzle DB wird direkt importiert
  }

  /**
   * Singleton Instance abrufen
   */
  static getInstance(): RepositoryFactoryImpl {
    if (!RepositoryFactoryImpl.instance) {
      RepositoryFactoryImpl.instance = new RepositoryFactoryImpl();
    }
    return RepositoryFactoryImpl.instance;
  }

  /**
   * Dispatch Repository abrufen
   */
  dispatch(): DispatchRepository {
    if (!this._dispatchRepository) {
      this._dispatchRepository = new DispatchRepositoryImpl();
    }
    return this._dispatchRepository;
  }

  /**
   * Warehouse Repository abrufen
   */
  warehouse(): WarehouseRepository {
    if (!this._warehouseRepository) {
      this._warehouseRepository = new WarehouseRepositoryImpl();
    }
    return this._warehouseRepository;
  }

  /**
   * Cutting Repository abrufen
   */
  cutting(): CuttingRepository {
    if (!this._cuttingRepository) {
      this._cuttingRepository = new CuttingRepositoryImpl();
    }
    return this._cuttingRepository;
  }

  /**
   * Repository Statistics für Monitoring
   */
  async getAllRepositoryStats() {
    const stats = {
      timestamp: new Date().toISOString(),
      repositories: {} as any
    };

    // Dispatch Repository Stats
    if (this._dispatchRepository) {
      stats.repositories.dispatch = await this._dispatchRepository.getStats();
    }

    // Warehouse Repository Stats
    if (this._warehouseRepository) {
      stats.repositories.warehouse = await this._warehouseRepository.getStats();
    }

    // Cutting Repository Stats
    if (this._cuttingRepository) {
      stats.repositories.cutting = await this._cuttingRepository.getStats();
    }

    return stats;
  }

  /**
   * Alle Repository-Caches invalidieren
   */
  async invalidateAllCaches(): Promise<void> {
    const invalidationPromises: Promise<void>[] = [];

    if (this._dispatchRepository) {
      invalidationPromises.push(this._dispatchRepository.invalidateCache());
    }

    if (this._warehouseRepository) {
      invalidationPromises.push(this._warehouseRepository.invalidateCache());
    }

    if (this._cuttingRepository) {
      invalidationPromises.push(this._cuttingRepository.invalidateCache());
    }

    await Promise.all(invalidationPromises);
  }

  /**
   * Factory zurücksetzen (für Testing)
   */
  static reset(): void {
    RepositoryFactoryImpl.instance = undefined as any;
  }
}

/**
 * Globale Repository Factory Instance
 */
let repositoryFactory: RepositoryFactoryImpl | undefined;

/**
 * Repository Factory initialisieren
 */
export function initializeRepositoryFactory(): RepositoryFactoryImpl {
  repositoryFactory = RepositoryFactoryImpl.getInstance();
  return repositoryFactory;
}

/**
 * Repository Factory abrufen
 */
export function getRepositoryFactory(): RepositoryFactoryImpl {
  if (!repositoryFactory) {
    throw new Error('Repository factory not initialized. Call initializeRepositoryFactory first.');
  }
  return repositoryFactory;
}

/**
 * Direkte Repository-Zugriffe (Convenience Functions)
 */
export function getDispatchRepository(): DispatchRepository {
  return getRepositoryFactory().dispatch();
}

export function getWarehouseRepository(): WarehouseRepository {
  return getRepositoryFactory().warehouse();
}

export function getCuttingRepository(): CuttingRepository {
  return getRepositoryFactory().cutting();
}