import express from 'express';
import { db } from '../db';
import { ariL } from '../db/schema';
import { desc } from 'drizzle-orm';

const router = express.Router();

// Debug-Route für ARiL-Daten
router.get('/debug', async (req, res) => {
  try {
    console.log('🔍 ARiL Debug-Route aufgerufen');
    
    // Teste direkte Drizzle-Abfrage für ARiL-Daten
    const result = await db.select().from(ariL)
      .limit(5) // Nur erste 5 Datensätze für Debug
      .orderBy(desc(ariL.datum));
    
    console.log('✅ ARiL Daten aus Datenbank:', result);
    
    res.json({
      success: true,
      count: result.length,
      data: result,
      fields: result.length > 0 ? Object.keys(result[0]) : [],
    });
  } catch (error) {
    console.error('❌ ARiL Debug Fehler:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unbekannter Fehler',
      details: error
    });
  }
});

export default router;