import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as schema from './schema';
import path from 'path';

// Database path - using the same SQLite database as Prisma
const dbPath = process.env.DATABASE_URL_SFM_DASHBOARD?.replace('file:', '') || 
  path.join(process.cwd(), 'prisma-sfm-dashboard', 'dev.db');

// Create SQLite connection
const sqlite = new Database(dbPath);

// Enable WAL mode for better performance
sqlite.pragma('journal_mode = WAL');

// Create Drizzle instance with schema
export const db = drizzle(sqlite, { schema });

// Export schema for use in repositories
export * from './schema';

// Export types
export type Database = typeof db;