/**
 * Warehouse Repository Implementation
 * 
 * Implementiert die WarehouseRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Lagerdaten.
 */

import { db } from '../db';
import { eq, gte, lte, desc, asc, and } from 'drizzle-orm';
import {
  WarehouseRepository,
  DateRange,
  WarehouseOverview,
  CapacityTrend,
  CapacityAlert,
  RepositoryStats
} from './interfaces';
import { auslastung200, auslastung240, ariL, atrL } from '../db/schema';
import {
  Lagerauslastung200DataPoint,
  Lagerauslastung240DataPoint,
  ArilDataPoint,
  AtrlDataPoint
} from '../types/database.types';
import { getBackendCache, BackendCacheKeyGenerator } from '../services/cache.service';

/**
 * Cache-TTL für Warehouse-Daten (verschiedene Datentypen)
 */
const WAREHOUSE_CACHE_TTL = {
  CAPACITY: 2 * 60 * 1000, // 2 Minuten - Auslastungsdaten
  ARIL_ATRL: 1 * 60 * 1000, // 1 Minute - Hochfrequente Lagerdaten
  OVERVIEW: 5 * 60 * 1000, // 5 Minuten - Übersichtsdaten
  TRENDS: 10 * 60 * 1000, // 10 Minuten - Trend-Analysen
  ALERTS: 3 * 60 * 1000 // 3 Minuten - Kritische Warnungen
};

export class WarehouseRepositoryImpl implements WarehouseRepository {
  private db = db;
  private cache = getBackendCache();
  private stats: RepositoryStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    avgQueryTime: 0,
    lastAccessed: new Date()
  };

  constructor() {
    // Drizzle DB wird direkt importiert
  }

  /**
   * Lagerauslastung 200 Daten abrufen
   */
  async getLagerauslastung200Data(dateRange?: DateRange): Promise<Lagerauslastung200DataPoint[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.warehouse.lagerauslastung200(dateRange);
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          if (dateRange && dateRange.startDate && dateRange.endDate) {
            const startDate = new Date(dateRange.startDate);
            const endDate = new Date(dateRange.endDate);
            
            return await this.db.select().from(auslastung200)
              .where(
                and(
                  gte(auslastung200.aufnahmeDatum, startDate),
                  lte(auslastung200.aufnahmeDatum, endDate)
                )
              )
              .orderBy(desc(auslastung200.aufnahmeDatum))
              .limit(1000);
          }
          
          return await this.db.select().from(auslastung200)
            .orderBy(desc(auslastung200.aufnahmeDatum))
            .limit(1000);
        },
        WAREHOUSE_CACHE_TTL.CAPACITY
      );

      const formattedData: Lagerauslastung200DataPoint[] = data.map((item: any) => ({
        date: item.aufnahmeDatum || '',
        auslastungA: parseFloat(item.auslastungA || '0'),
        auslastungB: parseFloat(item.auslastungB || '0'),
        auslastungC: parseFloat(item.auslastungC || '0'),
        gesamt: parseFloat(item.auslastungA || '0') + parseFloat(item.auslastungB || '0') + parseFloat(item.auslastungC || '0')
      }));

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return formattedData;

    } catch (error) {
      console.error('Error fetching Lagerauslastung200Data:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Lagerauslastung 240 Daten abrufen
   */
  async getLagerauslastung240Data(dateRange?: DateRange): Promise<Lagerauslastung240DataPoint[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.warehouse.lagerauslastung240(dateRange);
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          if (dateRange && dateRange.startDate && dateRange.endDate) {
            const startDate = new Date(dateRange.startDate);
            const endDate = new Date(dateRange.endDate);
            
            return await this.db.select().from(auslastung240)
              .where(
                and(
                  gte(auslastung240.aufnahmeDatum, startDate),
                  lte(auslastung240.aufnahmeDatum, endDate)
                )
              )
              .orderBy(desc(auslastung240.aufnahmeDatum))
              .limit(1000);
          }
          
          return await this.db.select().from(auslastung240)
            .orderBy(desc(auslastung240.aufnahmeDatum))
            .limit(1000);
        },
        WAREHOUSE_CACHE_TTL.CAPACITY
      );

      const formattedData: Lagerauslastung240DataPoint[] = data.map((item: any) => ({
        date: item.aufnahmeDatum || '',
        auslastungA: parseFloat(item.auslastungA || '0'),
        auslastungB: parseFloat(item.auslastungB || '0'),
        auslastungC: parseFloat(item.auslastungC || '0'),
        gesamt: parseFloat(item.auslastungA || '0') + parseFloat(item.auslastungB || '0') + parseFloat(item.auslastungC || '0')
      }));

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return formattedData;

    } catch (error) {
      console.error('Error fetching Lagerauslastung240Data:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * ARiL Lagerdaten abrufen
   */
  async getArilData(dateRange?: DateRange): Promise<ArilDataPoint[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.warehouse.aril(dateRange);
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          if (dateRange && dateRange.startDate && dateRange.endDate) {
            const startDate = new Date(dateRange.startDate);
            const endDate = new Date(dateRange.endDate);
            
            return await this.db.select().from(ariL)
              .where(
                and(
                  gte(ariL.datum, startDate),
                  lte(ariL.datum, endDate)
                )
              )
              .orderBy(desc(ariL.datum))
              .limit(1000);
          }
          
          return await this.db.select().from(ariL)
            .orderBy(desc(ariL.datum))
            .limit(1000);
        },
        WAREHOUSE_CACHE_TTL.ARIL_ATRL
      );

      const formattedData: ArilDataPoint[] = data.map((item: any) => ({
        id: item.id,
        Datum: item.Datum || '',
        waTaPositionen: item.waTaPositionen || 0,
        cuttingLagerKunde: item.cuttingLagerKunde || 0,
        cuttingLagerRest: item.cuttingLagerRest || 0,
        Umlagerungen: item.Umlagerungen || 0,
        lagerCutting: item.lagerCutting || 0
      }));

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return formattedData;

    } catch (error) {
      console.error('Error fetching ArilData:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * ATrL Lagerdaten abrufen
   */
  async getAtrlData(dateRange?: DateRange): Promise<AtrlDataPoint[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.warehouse.atrl(dateRange);
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          if (dateRange && dateRange.startDate && dateRange.endDate) {
            const startDate = new Date(dateRange.startDate);
            const endDate = new Date(dateRange.endDate);
            
            return await this.db.select().from(atrL)
              .where(
                and(
                  gte(atrL.datum, startDate),
                  lte(atrL.datum, endDate)
                )
              )
              .orderBy(desc(atrL.datum))
              .limit(1000);
          }
          
          return await this.db.select().from(atrL)
            .orderBy(desc(atrL.datum))
            .limit(1000);
        },
        WAREHOUSE_CACHE_TTL.ARIL_ATRL
      );

      const formattedData: AtrlDataPoint[] = data.map((item: any) => ({
        id: item.id,
        Datum: item.Datum || '',
        weAtrl: item.weAtrl,
        EinlagerungAblKunde: item.EinlagerungAblKunde,
        EinlagerungAblRest: item.EinlagerungAblRest,
        umlagerungen: item.umlagerungen,
        waTaPositionen: item.waTaPositionen,
        AuslagerungAbl: item.AuslagerungAbl
      }));

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return formattedData;

    } catch (error) {
      console.error('Error fetching AtrlData:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Bestandsübersicht für alle Lager
   */
  async getWarehouseOverview(): Promise<WarehouseOverview> {
    const startTime = Date.now();
    const cacheKey = 'warehouse:overview';
    
    this.stats.totalQueries++;

    try {
      const overview = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const [latest200, latest240, latestAril, latestAtrl] = await Promise.all([
            this.db.select().from(auslastung200).orderBy(desc(auslastung200.aufnahmeDatum)).limit(1).then(results => results[0] || null),
            this.db.select().from(auslastung240).orderBy(desc(auslastung240.aufnahmeDatum)).limit(1).then(results => results[0] || null),
            this.db.select().from(ariL).orderBy(desc(ariL.datum)).limit(1).then(results => results[0] || null),
             this.db.select().from(atrL).orderBy(desc(atrL.datum)).limit(1).then(results => results[0] || null)
          ]);

          const total200 = ((latest200?.auslastungA || 0) + 
                           (latest200?.auslastungB || 0) + 
                           (latest200?.auslastungC || 0));
          
          const total240 = ((latest240?.auslastungA || 0) + 
                           (latest240?.auslastungB || 0) + 
                           (latest240?.auslastungC || 0));

          return {
            warehouse200: {
              currentCapacity: total200,
              maxCapacity: 1000,
              utilizationRate: total200 / 1000,
              trend: 'stable' as const
            },
            warehouse240: {
              currentCapacity: total240,
              maxCapacity: 1200,
              utilizationRate: total240 / 1200,
              trend: 'stable' as const
            },
            aril: {
              totalPositions: 2000,
              filledPositions: latestAril?.waTaPositionen || 0,
              fillRate: (latestAril?.waTaPositionen || 0) / 2000,
              lastUpdate: latestAril?.datum ? 
                (latestAril.datum instanceof Date ? latestAril.datum.toISOString() : latestAril.datum) : 
                new Date().toISOString()
            },
            atrl: {
              totalPositions: 1500,
              filledPositions: latestAtrl?.waTaPositionen || 0,
              fillRate: (latestAtrl?.waTaPositionen || 0) / 1500,
              lastUpdate: latestAtrl?.datum ? 
                (latestAtrl.datum instanceof Date ? latestAtrl.datum.toISOString() : latestAtrl.datum) : 
                new Date().toISOString()
            }
          };
        },
        WAREHOUSE_CACHE_TTL.OVERVIEW
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return overview;

    } catch (error) {
      console.error('Error fetching warehouse overview:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Auslastungstrends über Zeit
   */
  async getCapacityTrends(warehouseType: '200' | '240', dateRange: DateRange): Promise<CapacityTrend[]> {
    const startTime = Date.now();
    const cacheKey = `warehouse:trends:${warehouseType}:${dateRange.startDate}:${dateRange.endDate}`;
    
    this.stats.totalQueries++;

    try {
      const trends = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const maxCapacity = warehouseType === '200' ? 1000 : 1200;

          let data: any[];
          const startDate = dateRange.startDate ? new Date(dateRange.startDate) : null;
          const endDate = dateRange.endDate ? new Date(dateRange.endDate) : null;

          if (warehouseType === '200') {
            if (startDate && endDate) {
              data = await this.db.select().from(auslastung200)
                .where(and(
                  gte(auslastung200.aufnahmeDatum, startDate),
                  lte(auslastung200.aufnahmeDatum, endDate)
                ))
                .orderBy(asc(auslastung200.aufnahmeDatum));
            } else if (startDate) {
              data = await this.db.select().from(auslastung200)
                .where(gte(auslastung200.aufnahmeDatum, startDate))
                .orderBy(asc(auslastung200.aufnahmeDatum));
            } else if (endDate) {
              data = await this.db.select().from(auslastung200)
                .where(lte(auslastung200.aufnahmeDatum, endDate))
                .orderBy(asc(auslastung200.aufnahmeDatum));
            } else {
              data = await this.db.select().from(auslastung200)
                .orderBy(asc(auslastung200.aufnahmeDatum));
            }
          } else {
            if (startDate && endDate) {
              data = await this.db.select().from(auslastung240)
                .where(and(
                  gte(auslastung240.aufnahmeDatum, startDate),
                  lte(auslastung240.aufnahmeDatum, endDate)
                ))
                .orderBy(asc(auslastung240.aufnahmeDatum));
            } else if (startDate) {
              data = await this.db.select().from(auslastung240)
                .where(gte(auslastung240.aufnahmeDatum, startDate))
                .orderBy(asc(auslastung240.aufnahmeDatum));
            } else if (endDate) {
              data = await this.db.select().from(auslastung240)
                .where(lte(auslastung240.aufnahmeDatum, endDate))
                .orderBy(asc(auslastung240.aufnahmeDatum));
            } else {
              data = await this.db.select().from(auslastung240)
                .orderBy(asc(auslastung240.aufnahmeDatum));
            }
          }

          return data.map((item: any, index: number) => {
            const totalCapacity = parseFloat(item.auslastungA || '0') + 
                                parseFloat(item.auslastungB || '0') + 
                                parseFloat(item.auslastungC || '0');
            const utilizationRate = totalCapacity / maxCapacity;
            
            const previousCapacity = index > 0 ? 
              (parseFloat(data[index-1].auslastungA || '0') + 
               parseFloat(data[index-1].auslastungB || '0') + 
               parseFloat(data[index-1].auslastungC || '0')) : 
              totalCapacity;
            
            return {
              date: item.aufnahmeDatum || '',
              capacity: totalCapacity,
              utilizationRate,
              changeFromPrevious: totalCapacity - previousCapacity
            };
          });
        },
        WAREHOUSE_CACHE_TTL.TRENDS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return trends;

    } catch (error) {
      console.error(`Error fetching capacity trends for warehouse ${warehouseType}:`, error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Kritische Auslastungswerte ermitteln
   */
  async getCriticalCapacityAlerts(threshold: number = 0.9): Promise<CapacityAlert[]> {
    const startTime = Date.now();
    const cacheKey = `warehouse:alerts:${threshold}`;
    
    this.stats.totalQueries++;

    try {
      const alerts = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const alerts: CapacityAlert[] = [];

          const [latest200, latest240] = await Promise.all([
            this.db.select().from(auslastung200).orderBy(desc(auslastung200.aufnahmeDatum)).limit(1).then(results => results[0] || null),
            this.db.select().from(auslastung240).orderBy(desc(auslastung240.aufnahmeDatum)).limit(1).then(results => results[0] || null)
          ]);

          if (latest200) {
            const total200 = (latest200.auslastungA || 0) + 
                           (latest200.auslastungB || 0) + 
                           (latest200.auslastungC || 0);
            const utilization200 = total200 / 1000;
            
            if (utilization200 >= threshold) {
              alerts.push({
                warehouseType: 'Warehouse 200',
                currentUtilization: utilization200,
                threshold,
                severity: utilization200 >= 0.95 ? 'critical' : 'warning',
                recommendation: utilization200 >= 0.95 ? 
                  'Sofortige Auslagerung erforderlich' : 
                  'Auslagerung in den nächsten 24h planen',
                date: latest200.aufnahmeDatum instanceof Date ? latest200.aufnahmeDatum.toISOString().split('T')[0] : (latest200.aufnahmeDatum || '')
              });
            }
          }

          if (latest240) {
            const total240 = (latest240.auslastungA || 0) + 
                           (latest240.auslastungB || 0) + 
                           (latest240.auslastungC || 0);
            const utilization240 = total240 / 1200;
            
            if (utilization240 >= threshold) {
              alerts.push({
                warehouseType: 'Warehouse 240',
                currentUtilization: utilization240,
                threshold,
                severity: utilization240 >= 0.95 ? 'critical' : 'warning',
                recommendation: utilization240 >= 0.95 ? 
                  'Sofortige Auslagerung erforderlich' : 
                  'Auslagerung in den nächsten 24h planen',
                date: latest240.aufnahmeDatum instanceof Date ? latest240.aufnahmeDatum.toISOString().split('T')[0] : (latest240.aufnahmeDatum || '')
              });
            }
          }

          return alerts;
        },
        WAREHOUSE_CACHE_TTL.ALERTS
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return alerts;

    } catch (error) {
      console.error('Error fetching critical capacity alerts:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Repository-Statistiken abrufen
   */
  async getStats(): Promise<RepositoryStats> {
    this.stats.hitRate = this.stats.totalQueries > 0 ? 
      (this.stats.cacheHits / this.stats.totalQueries) * 100 : 0;
    this.stats.lastAccessed = new Date();
    
    return { ...this.stats };
  }

  /**
   * Repository-Cache invalidieren
   */
  async invalidateCache(key?: string): Promise<void> {
    if (key) {
      this.cache.invalidateByDataTypes([key]);
    } else {
      this.cache.invalidateByDataTypes(['warehouse']);
    }
  }

  /**
   * Statistiken aktualisieren
   */
  private updateStats(startTime: number): void {
    const queryTime = Date.now() - startTime;
    this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
    this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
    this.stats.lastAccessed = new Date();
  }
}