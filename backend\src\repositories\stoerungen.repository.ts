import { db } from '../db';
import { stoerungen, stoerungsComments, systemStatus, systemStatusMessage } from '../db/schema';
import { eq, and, or, desc, asc, count, avg, inArray, gte, isNotNull } from 'drizzle-orm';
import { 
  StoerungCreateData, 
  StoerungUpdateData, 
  StoerungWithComments, 
  StoerungCommentCreateData,
  StoerungsStats,
  SystemStatusData,
  SystemStatusUpdateData
} from '../types/stoerungen.types';

// Simple in-memory cache for störungen
const simpleCache = new Map<string, { data: any; expires: number }>();

const getCached = <T>(key: string): T | null => {
  const entry = simpleCache.get(key);
  if (entry && entry.expires > Date.now()) {
    return entry.data;
  }
  simpleCache.delete(key);
  return null;
};

const setCache = (key: string, data: any, ttlMs: number = 300000) => {
  simpleCache.set(key, { data, expires: Date.now() + ttlMs });
};

const clearCachePattern = (pattern: string) => {
  for (const key of simpleCache.keys()) {
    if (key.includes(pattern)) {
      simpleCache.delete(key);
    }
  }
};

export class StoerungenRepository {
  private static instance: StoerungenRepository;
  private db = db;

  constructor() {}

  static getInstance(): StoerungenRepository {
    if (!StoerungenRepository.instance) {
      StoerungenRepository.instance = new StoerungenRepository();
    }
    return StoerungenRepository.instance;
  }

  async createStoerung(data: StoerungCreateData) {
    const tagsJson = data.tags ? JSON.stringify(data.tags) : null;
    
    const result = await this.db.insert(stoerungen).values({
      ...data,
      tags: tagsJson,
      updatedAt: Date.now(),
    }).returning();

    const stoerung = result[0];

    // Fetch comments separately
    const comments = await this.db.select().from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    clearCachePattern('stoerungen');
    return this.formatStoerung({ ...stoerung, comments });
  }

  async getStoerungen(options?: {
    status?: string;
    severity?: string;
    category?: string;
    affected_system?: string;
    limit?: number;
    offset?: number;
  }) {
    const cacheKey = `stoerungen:list:${JSON.stringify(options || {})}`;
    const cached = getCached<StoerungWithComments[]>(cacheKey);
    if (cached) return cached;

    const conditions: any[] = [];
    if (options?.status) conditions.push(eq(stoerungen.status, options.status));
    if (options?.severity) conditions.push(eq(stoerungen.severity, options.severity));
    if (options?.category) conditions.push(eq(stoerungen.category, options.category));
    if (options?.affected_system) conditions.push(eq(stoerungen.affectedSystem, options.affected_system));

    let query = this.db.select().from(stoerungen)
      .orderBy(desc(stoerungen.createdAt));
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    if (options?.limit) {
      query = query.limit(options.limit);
    }
    
    if (options?.offset) {
      query = query.offset(options.offset);
    }

    const stoerungsResult = await query;

    // Fetch comments for each störung
    const stoerungsWithComments = await Promise.all(
      stoerungsResult.map(async (stoerung) => {
        const comments = await this.db.select().from(stoerungsComments)
          .where(eq(stoerungsComments.stoerungId, stoerung.id))
          .orderBy(desc(stoerungsComments.createdAt));
        return { ...stoerung, comments };
      })
    );

    const formatted = stoerungsWithComments.map(this.formatStoerung);
    setCache(cacheKey, formatted, 300000); // 5 minutes TTL
    return formatted;
  }

  async getStoerungById(id: number): Promise<StoerungWithComments | null> {
    const cacheKey = `stoerungen:detail:${id}`;
    const cached = getCached<StoerungWithComments>(cacheKey);
    if (cached) return cached;

    const result = await this.db.select().from(stoerungen)
      .where(eq(stoerungen.id, id))
      .limit(1);

    const stoerung = result[0];
    if (!stoerung) return null;

    // Fetch comments separately
    const comments = await this.db.select().from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    const formatted = this.formatStoerung({ ...stoerung, comments });
    setCache(cacheKey, formatted, 300000);
    return formatted;
  }

  async updateStoerung(id: number, data: StoerungUpdateData) {
    const updateData: any = { ...data };
    
    if (data.tags) {
      updateData.tags = JSON.stringify(data.tags);
    }

    if (data.status === 'RESOLVED' && !data.resolved_at) {
      updateData.resolvedAt = Date.now();
    }

    updateData.updatedAt = Date.now();

    const result = await this.db.update(stoerungen)
      .set(updateData)
      .where(eq(stoerungen.id, id))
      .returning();

    const stoerung = result[0];

    // Fetch comments separately
    const comments = await this.db.select().from(stoerungsComments)
      .where(eq(stoerungsComments.stoerungId, stoerung.id))
      .orderBy(desc(stoerungsComments.createdAt));

    clearCachePattern('stoerungen');
    return this.formatStoerung({ ...stoerung, comments });
  }

  async deleteStoerung(id: number): Promise<boolean> {
    try {
      await this.db.delete(stoerungen)
        .where(eq(stoerungen.id, id));
      clearCachePattern('stoerungen');
      return true;
    } catch {
      return false;
    }
  }

  async addComment(data: StoerungCommentCreateData) {
    const result = await this.db.insert(stoerungsComments)
      .values(data)
      .returning();

    clearCachePattern('stoerungen');
    return result[0];
  }

  async getStoerungsStats(): Promise<StoerungsStats> {
    const cacheKey = 'stoerungen:stats';
    const cached = getCached<StoerungsStats>(cacheKey);
    if (cached) return cached;

    const [totalResult, activeResult, resolvedResult, avgMttrResult, recentResult] = await Promise.all([
      this.db.select({ count: count() }).from(stoerungen),
      this.db.select({ count: count() }).from(stoerungen)
        .where(inArray(stoerungen.status, ['NEW', 'IN_PROGRESS'])),
      this.db.select({ count: count() }).from(stoerungen)
        .where(eq(stoerungen.status, 'RESOLVED')),
      this.db.select({ avg: avg(stoerungen.mttrMinutes) }).from(stoerungen)
        .where(and(eq(stoerungen.status, 'RESOLVED'), isNotNull(stoerungen.mttrMinutes))),
      this.db.select({ count: count() }).from(stoerungen)
        .where(and(
          eq(stoerungen.status, 'RESOLVED'),
          gte(stoerungen.resolvedAt, Date.now() - 24 * 60 * 60 * 1000)
        ))
    ]);

    // Get severity counts separately
    const severityResults = await Promise.all([
      this.db.select({ count: count() }).from(stoerungen).where(eq(stoerungen.severity, 'CRITICAL')),
      this.db.select({ count: count() }).from(stoerungen).where(eq(stoerungen.severity, 'HIGH')),
      this.db.select({ count: count() }).from(stoerungen).where(eq(stoerungen.severity, 'MEDIUM')),
      this.db.select({ count: count() }).from(stoerungen).where(eq(stoerungen.severity, 'LOW'))
    ]);

    const stats: StoerungsStats = {
      total: totalResult[0]?.count || 0,
      active: activeResult[0]?.count || 0,
      resolved: resolvedResult[0]?.count || 0,
      critical: severityResults[0][0]?.count || 0,
      high: severityResults[1][0]?.count || 0,
      medium: severityResults[2][0]?.count || 0,
      low: severityResults[3][0]?.count || 0,
      avg_mttr_minutes: Math.round(avgMttrResult[0]?.avg || 0),
      resolution_rate_24h: recentResult[0]?.count || 0,
    };

    setCache(cacheKey, stats, 900000); // 15 minutes TTL
    return stats;
  }

  async getSystemStatus(): Promise<SystemStatusData[]> {
    const cacheKey = 'system:status:all';
    const cached = getCached<SystemStatusData[]>(cacheKey);
    if (cached) return cached;

    const statuses = await this.db.select().from(systemStatus)
      .orderBy(asc(systemStatus.systemName));

    // Load status messages for each system status with category-based selection
    const statusesWithMessages = await Promise.all(
      statuses.map(async (status) => {
        // Determine category based on system name
        const category = this.determineSystemCategory(status.systemName);
        
        // Get detailed status messages for this category and status
        const statusMessages = await this.db.select().from(systemStatusMessage)
          .where(and(
            eq(systemStatusMessage.category, category),
            eq(systemStatusMessage.status, status.status),
            eq(systemStatusMessage.isActive, 1)
          ))
          .orderBy(asc(systemStatusMessage.priority))
          .limit(3);

        return {
          ...status,
          status: status.status as 'OK' | 'WARNING' | 'ERROR' | 'OFF',
          metadata: status.metadata ? JSON.parse(status.metadata) : null,
          statusMessages: statusMessages.map(msg => ({
            id: msg.id,
            title: msg.title,
            description: msg.description,
            priority: msg.priority,
            category: msg.category
          }))
        };
      })
    );

    setCache(cacheKey, statusesWithMessages, 30000); // 30 seconds TTL for live data
    return statusesWithMessages;
  }

  async updateSystemStatus(data: SystemStatusUpdateData): Promise<SystemStatusData> {
    const metadataJson = data.metadata ? JSON.stringify(data.metadata) : null;

    // First try to find existing record
    const existingStatusResult = await this.db.select().from(systemStatus)
      .where(eq(systemStatus.systemName, data.system_name))
      .limit(1);

    const existingStatus = existingStatusResult[0];
    let status;
    
    if (existingStatus) {
      // Update existing record
      const updateResult = await this.db.update(systemStatus)
        .set({
          status: data.status,
          metadata: metadataJson,
          lastCheck: Date.now(),
          updatedAt: Date.now(),
        })
        .where(eq(systemStatus.id, existingStatus.id))
        .returning();
      status = updateResult[0];
    } else {
      // Create new record
      const insertResult = await this.db.insert(systemStatus)
        .values({
          systemName: data.system_name,
          status: data.status,
          metadata: metadataJson,
          updatedAt: Date.now(),
        })
        .returning();
      status = insertResult[0];
    }

    clearCachePattern('system:status');
    
    return {
      ...status,
      status: status.status as 'OK' | 'WARNING' | 'ERROR',
      metadata: status.metadata ? JSON.parse(status.metadata) : null,
    };
  }

  private formatStoerung(stoerung: any): StoerungWithComments {
    let tags: string[] = [];
    
    try {
      if (stoerung.tags) {
        if (typeof stoerung.tags === 'string') {
          // Try to parse as JSON first
          try {
            tags = JSON.parse(stoerung.tags);
          } catch {
            // If JSON parsing fails, split by comma
            tags = stoerung.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean);
          }
        } else if (Array.isArray(stoerung.tags)) {
          tags = stoerung.tags;
        }
      }
    } catch (error) {
      console.error('Error parsing tags for störung:', stoerung.id, error);
      tags = [];
    }

    return {
      ...stoerung,
      tags,
    };
  }

  /**
   * Determine system category based on system name
   * This matches the categorization logic from SystemStatusHeatmap component
   */
  private determineSystemCategory(systemName: string): string {
    if (systemName.includes('Datenbank')) return 'Datenbanken';
    if (systemName.includes('Terminal')) return 'Terminals';
    if (systemName.includes('Fördertechnik')) return 'Fördertechnik';
    if (systemName.includes('Schrumpfanlage')) return 'Anlagen';
    if (systemName.includes('Wlan')) return 'Netzwerk';
    if (systemName.includes('Automatisches Trommellager') || systemName.includes('Automatisches Ringlager')) return 'Läger';
    if (systemName.includes('Stapler') || systemName.includes('FTS')) return 'Flurförderzeuge';
    if (systemName.includes('SAP')) return 'SAP';
    if (systemName.includes('ITM')) return 'ITM';
    
    // Check for machine identifiers (M1-T-H3, M2-T-H3, etc.)
    const machineIdentifiers = [
      'M1-T-H3', 'M2-T-H3', 'M3-R-H3', 'M4-T-H3', 'M5-R-H3',
      'M6-T-H1', 'M7-R-H1', 'M8-T-H1', 'M9-R-H1', 'M10-T-H1',
      'M11-R-H1', 'M12-T-H1', 'M13-R-H1', 'M14-T-H1', 'M15-R-H1',
      'M16-T-H1', 'M17-R-H1', 'M18-T-H1', 'M19-T-H1', 'M20-T-H1',
      'M21-R-H1', 'M22-T-H3', 'M23-T-H1', 'M24-T-H3', 'M25-RR-H1',
      'M26-T-H1', 'M27-R-H3', 'M28-T-H1'
    ];
    
    if (machineIdentifiers.some(id => systemName.includes(id))) {
      return 'Maschinen';
    }
    
    // Default fallback
    return 'System';
  }
}