/**
 * Cutting Repository Implementation
 * 
 * Implementiert die CuttingRepository-Interface mit Caching
 * und optimierten Datenbankabfragen für Schnitt-/Cutting-Operationen.
 */

import { db } from '../db';
import {
  CuttingRepository,
  DateRange,
  CuttingChartData,
  LagerCutsChartData,
  MachineEfficiencyData,
  CuttingPerformanceOverview,
  TopMachine,
  MachineUtilization,
  RepositoryStats
} from './interfaces';
import {
  AblaengereiDataPoint,
  WEDataPoint
} from '../types/database.types';
import { getBackendCache, BackendCacheKeyGenerator } from '../services/cache.service';

/**
 * Cache-TTL für Cutting-Daten (verschiedene Datentypen)
 */
const CUTTING_CACHE_TTL = {
  ABLAENGEREI: 2 * 60 * 1000, // 2 Minuten
  WE_DATA: 3 * 60 * 1000, // 3 Minuten
  CHART_DATA: 5 * 60 * 1000, // 5 Minuten
  EFFICIENCY: 10 * 60 * 1000, // 10 Minuten
  PERFORMANCE: 15 * 60 * 1000, // 15 Minuten
  MACHINES: 20 * 60 * 1000 // 20 Minuten
};

export class CuttingRepositoryImpl implements CuttingRepository {
  private db = db;
  private cache = getBackendCache();
  private stats: RepositoryStats = {
    totalQueries: 0,
    cacheHits: 0,
    cacheMisses: 0,
    hitRate: 0,
    avgQueryTime: 0,
    lastAccessed: new Date()
  };

  constructor() {
    // Drizzle DB wird direkt importiert
  }

  /**
   * Ablaengerei-Daten abrufen
   */
  async getAblaengereiData(dateRange?: DateRange): Promise<AblaengereiDataPoint[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.cutting.ablaengerei(dateRange);
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const whereClause = dateRange ? {
            Datum: {
              gte: dateRange.startDate,
              lte: dateRange.endDate
            }
          } : {};

          return await this.db.ablaengerei.findMany({
            where: whereClause,
            orderBy: { Datum: 'desc' },
            take: 1000
          });
        },
        CUTTING_CACHE_TTL.ABLAENGEREI
      );

      const formattedData: AblaengereiDataPoint[] = data.map((item: any) => ({
        id: item.id,
        datum: item.Datum || '',
        cutTT: item.cutTT,
        cutTR: item.cutTR,
        cutRR: item.cutRR,
        pickCut: item.pickCut,
        lagerCut220: item.lagerCut220,
        lagerCut240: item.lagerCut240,
        lagerCut200: item.lagerCut200,
        cutLagerK200: item.cutLagerK200,
        cutLagerK240: item.cutLagerK240,
        cutLagerK220: item.cutLagerK220,
        cutLager200: item.cutLager200,
        cutLagerR240: item.cutLagerR240,
        cutLagerR220: item.cutLagerR220
      }));

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return formattedData;

    } catch (error) {
      console.error('Error fetching AblaengereiData:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Wareneingang (WE) Daten abrufen
   */
  async getWEData(dateRange?: DateRange): Promise<WEDataPoint[]> {
    const startTime = Date.now();
    const cacheKey = BackendCacheKeyGenerator.cutting.weData(dateRange);
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          const whereClause = dateRange ? {
            Datum: {
              gte: dateRange.startDate,
              lte: dateRange.endDate
            }
          } : {};

          return await this.db.wE.findMany({
            where: whereClause,
            orderBy: { Datum: 'desc' },
            take: 1000
          });
        },
        CUTTING_CACHE_TTL.WE_DATA
      );

      const formattedData: WEDataPoint[] = data.map((item: any) => ({
        id: item.id,
        datum: item.Datum || '',
        weAtrl: item.weAtrl,
        weManl: item.weManl
      }));

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return formattedData;

    } catch (error) {
      console.error('Error fetching WEData:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Schnittdaten für Charts
   */
  async getCuttingChartData(dateRange?: DateRange): Promise<CuttingChartData[]> {
    const ablaengereiData = await this.getAblaengereiData(dateRange);

    return ablaengereiData.map(item => ({
      name: item.datum,
      date: item.datum,
      cutTT: item.cutTT || 0,
      cutTR: item.cutTR || 0,
      cutRR: item.cutRR || 0,
      pickCut: item.pickCut || 0
    }));
  }

  /**
   * Lager-Cuts Chart-Daten
   */
  async getLagerCutsChartData(dateRange?: DateRange): Promise<LagerCutsChartData[]> {
    const ablaengereiData = await this.getAblaengereiData(dateRange);

    return ablaengereiData.map(item => {
      const lagerSumme = (item.lagerCut200 || 0) + (item.lagerCut220 || 0) + (item.lagerCut240 || 0);
      const cutLagerKSumme = (item.cutLagerK200 || 0) + (item.cutLagerK220 || 0) + (item.cutLagerK240 || 0);
      const cutLagerRSumme = (item.cutLager200 || 0) + (item.cutLagerR220 || 0) + (item.cutLagerR240 || 0);

      return {
        name: item.datum,
        date: item.datum,
        lagerSumme,
        cutLagerKSumme,
        cutLagerRSumme
      };
    });
  }

  /**
   * Maschinen-Effizienz-Daten
   */
  async getMaschinenEfficiency(dateRange?: DateRange): Promise<MachineEfficiencyData[]> {
    const startTime = Date.now();
    const cacheKey = `cutting:efficiency:${dateRange ? `${dateRange.startDate}:${dateRange.endDate}` : 'all'}`;
    
    this.stats.totalQueries++;

    try {
      // Maschinen-Daten für Soll-Schnitte abrufen
      const maschinenData = await this.cache.cachedQuery(
        'cutting:maschinen:all',
        async () => {
          // Explizit alle Felder anfordern, insbesondere schnitteProStd
          return await this.db.maschinen.findMany({
            select: {
              id: true,
              Machine: true,
              schnitteProStd: true
            }
          });
        },
        CUTTING_CACHE_TTL.MACHINES
      );

      // Schnitte-Daten abrufen und nach Maschine und Datum gruppieren
      const schnitteData = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          // Bestimme den Datumsbereich für die Abfrage
          let whereClause = {};
          if (dateRange) {
            whereClause = {
              Datum: {
                gte: dateRange.startDate,
                lte: dateRange.endDate
              }
            };
          }

          return await this.db.schnitte.findMany({
            where: whereClause,
            orderBy: { Datum: 'asc' },
            take: 500
          });
        },
        CUTTING_CACHE_TTL.EFFICIENCY
      );

      // Erstelle eine Map für schnellen Zugriff auf Soll-Schnitte pro Maschine
      const maschinenMap = new Map();
      
      maschinenData.forEach((machine: any) => {
        if (machine.Machine) {
          // Verwende den Wert aus der Datenbank und multipliziere mit 21 für Tageswert
          const sollWertProStunde = Number(machine.schnitteProStd);
          const sollWertProTag = sollWertProStunde * 21;
          
          // Speichere nur mit dem Format, das in der Datenbank verwendet wird (mit Bindestrich)
          maschinenMap.set(machine.Machine, sollWertProStunde);
        }
      });

      // Extrahiere alle Maschinennamen aus dem Schnitte-Schema
      const maschinenNamen = Object.keys(schnitteData[0] || {}).filter(key => 
        key.includes('_H1') || key.includes('_H3')
      );

      // Gruppiere Daten nach Datum und berechne Schnitte pro Maschine
      const groupedData = new Map();
      
      schnitteData.forEach((schnitt: any) => {
        if (!schnitt.Datum) return;
        
        // Für jede Maschine in diesem Datensatz
        maschinenNamen.forEach(maschinenKey => {
          // Konvertiere den Maschinennamen von Unterstrich zu Bindestrich für das Frontend
          // Stelle sicher, dass alle Unterstriche durch Bindestriche ersetzt werden
          const machineName = maschinenKey.replace(/_/g, '-');
          // Typensichere Zugriff auf die Schnitt-Daten
          const value = schnitt[maschinenKey as keyof typeof schnitt] as number | null || 0;
          
          if (value > 0) {
            const key = `${schnitt.Datum}_${machineName}`;
            
            if (!groupedData.has(key)) {
              // Bestimme die Halle (H1 oder H3) aus dem Maschinennamen
              const halle = machineName.includes('H1') ? 'H1' : 'H3';
              const maschinenTyp = machineName.split('-')[1]; // R oder T
              
              // Hole den Soll-Wert aus der Map (sollte jetzt für beide Formate verfügbar sein)
              const sollWert = maschinenMap.get(machineName) || 0;
              
              groupedData.set(key, {
                Datum: schnitt.Datum,
                Machine: machineName,
                Halle: halle,
                Typ: maschinenTyp,
                tagesSchnitte: 0,
                sollSchnitte: sollWert
              });
            }
            
            // Addiere die Schnitte für diese Maschine an diesem Tag
            const entry = groupedData.get(key);
            entry.tagesSchnitte += value;
          }
        });
      });

      // Berechne Effizienz-Daten
      const efficiencyData: MachineEfficiencyData[] = Array.from(groupedData.values()).map(item => {
        // Stelle sicher, dass wir einen gültigen Soll-Wert haben
        const sollSchnitteProStunde = item.sollSchnitte || 0;
        
        // Berechne Soll-Schnitte pro Tag (21 Stunden)
        const sollSchnitteProTag = sollSchnitteProStunde * 21; // 21h Schicht
        
        // Berechne Ist-Schnitte pro Stunde
        const istSchnitteProStunde = item.tagesSchnitte / 21; // Durchschnitt über 21h
        
        // Berechne Effizienz nur wenn Soll-Werte > 0 sind
        const effizienzProzent = sollSchnitteProTag > 0 ? 
          Math.min(Math.round((item.tagesSchnitte / sollSchnitteProTag) * 100), 200) : 0; // Max 200%

        return {
          Datum: item.Datum,
          Machine: item.Machine,
          sollSchnitte: sollSchnitteProStunde, // Stundenwert
          sollSchnitteProTag, // Tageswert (21h)
          tagesSchnitte: item.tagesSchnitte,
          istSchnitteProStunde,
          effizienzProzent
        };
      });

      // Wenn keine Daten gefunden wurden, logge dies
      if (efficiencyData.length === 0) {
        console.log('Keine Maschinen-Effizienz-Daten gefunden');
      }

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return efficiencyData;

    } catch (error) {
      console.error('Error fetching machine efficiency data:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Schnittdaten (Rohdaten)
   */
  async getSchnitteData(): Promise<any[]> {
    const startTime = Date.now();
    const cacheKey = 'cutting:schnitte:raw';
    
    this.stats.totalQueries++;

    try {
      const data = await this.cache.cachedQuery(
        cacheKey,
        async () => {
          return await this.db.schnitte.findMany({
            orderBy: { id: 'desc' },
            take: 1000
          });
        },
        CUTTING_CACHE_TTL.CHART_DATA
      );

      this.stats.cacheHits++;
      this.updateStats(startTime);
      return data;

    } catch (error) {
      console.error('Error fetching Schnitte data:', error);
      this.stats.cacheMisses++;
      this.updateStats(startTime);
      throw error;
    }
  }

  /**
   * Cutting-Performance-Übersicht
   */
  async getCuttingPerformanceOverview(dateRange?: DateRange): Promise<CuttingPerformanceOverview> {
    const [chartData, efficiencyData] = await Promise.all([
      this.getCuttingChartData(dateRange),
      this.getMaschinenEfficiency(dateRange)
    ]);

    const totalCuts = chartData.reduce((acc, item) => ({
      cutTT: acc.cutTT + item.cutTT,
      cutTR: acc.cutTR + item.cutTR,
      cutRR: acc.cutRR + item.cutRR,
      pickCut: acc.pickCut + item.pickCut
    }), { cutTT: 0, cutTR: 0, cutRR: 0, pickCut: 0 });

    const averageEfficiency = efficiencyData.length > 0 ? 
      efficiencyData.reduce((sum, item) => sum + item.effizienzProzent, 0) / efficiencyData.length : 0;

    const topMachine = efficiencyData.length > 0 ? 
      efficiencyData.reduce((best, current) => 
        current.effizienzProzent > best.effizienzProzent ? current : best
      ) : null;

    return {
      totalCuts,
      averageEfficiency,
      topMachine: topMachine ? {
        name: topMachine.Machine,
        efficiency: topMachine.effizienzProzent
      } : { name: 'N/A', efficiency: 0 },
      periodSummary: {
        startDate: dateRange?.startDate || chartData[chartData.length - 1]?.date || '',
        endDate: dateRange?.endDate || chartData[0]?.date || '',
        totalDays: chartData.length,
        averageCutsPerDay: chartData.length > 0 ? 
          (totalCuts.cutTT + totalCuts.cutTR + totalCuts.cutRR + totalCuts.pickCut) / chartData.length : 0
      },
      trends: {
        efficiency: 'stable',
        volume: 'stable'
      }
    };
  }

  /**
   * Top-Performance-Maschinen
   */
  async getTopPerformingMachines(limit: number = 5): Promise<TopMachine[]> {
    const efficiencyData = await this.getMaschinenEfficiency();

    const topMachines: TopMachine[] = efficiencyData
      .sort((a, b) => b.effizienzProzent - a.effizienzProzent)
      .slice(0, limit)
      .map((machine, index) => ({
        name: machine.Machine,
        averageEfficiency: machine.effizienzProzent,
        totalCuts: machine.tagesSchnitte,
        uptime: 21, // Standard 21h
        rank: index + 1
      }));

    return topMachines;
  }

  /**
   * Maschinen-Auslastungsanalyse
   */
  async getMachineUtilizationAnalysis(): Promise<MachineUtilization[]> {
    const efficiencyData = await this.getMaschinenEfficiency();

    return efficiencyData.map(machine => {
      const plannedHours = 21; // Standard 21h
      const actualHours = 21; // Vereinfacht
      const utilizationRate = 1.0; // Vereinfacht
      
      let status: 'optimal' | 'underutilized' | 'overutilized' = 'optimal';
      if (machine.effizienzProzent < 80) status = 'underutilized';
      else if (machine.effizienzProzent > 120) status = 'overutilized';

      return {
        machineName: machine.Machine,
        plannedHours,
        actualHours,
        utilizationRate,
        efficiency: machine.effizienzProzent,
        status
      };
    });
  }

  /**
   * Repository-Statistiken abrufen
   */
  async getStats(): Promise<RepositoryStats> {
    this.stats.hitRate = this.stats.totalQueries > 0 ? 
      (this.stats.cacheHits / this.stats.totalQueries) * 100 : 0;
    this.stats.lastAccessed = new Date();
    
    return { ...this.stats };
  }

  /**
   * Repository-Cache invalidieren
   */
  async invalidateCache(key?: string): Promise<void> {
    if (key) {
      this.cache.invalidateByDataTypes([key]);
    } else {
      this.cache.invalidateByDataTypes(['cutting']);
    }
  }

  /**
   * Statistiken aktualisieren
   */
  private updateStats(startTime: number): void {
    const queryTime = Date.now() - startTime;
    this.stats.avgQueryTime = ((this.stats.avgQueryTime * (this.stats.totalQueries - 1)) + queryTime) / this.stats.totalQueries;
    this.stats.hitRate = (this.stats.cacheHits / this.stats.totalQueries) * 100;
    this.stats.lastAccessed = new Date();
  }
}