import { sqliteTable, integer, text, real, index } from 'drizzle-orm/sqlite-core';

// DispatchData table - main table causing the current error
export const dispatchData = sqliteTable('dispatch_data', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: integer('datum', { mode: 'timestamp' }),
  tag: integer('tag'),
  monat: integer('monat'),
  kw: integer('kw'),
  jahr: integer('jahr'),
  servicegrad: real('servicegrad'),
  ausgeliefert_lup: integer('ausgeliefert_lup'),
  rueckstaendig: integer('rueckstaendig'),
  produzierte_tonnagen: real('produzierte_tonnagen'),
  direktverladung_kiaa: integer('direktverladung_kiaa'),
  umschlag: integer('umschlag'),
  kg_pro_colli: real('kg_pro_colli'),
  elefanten: integer('elefanten'),
  atrl: integer('atrl'),
  aril: integer('aril'),
  fuellgrad_aril: real('fuellgrad_aril'),
  qm_angenommen: integer('qm_angenommen'),
  qm_abgelehnt: integer('qm_abgelehnt'),
  qm_offen: integer('qm_offen'),
  mitarbeiter_std: real('mitarbeiter_std')
}, (table) => ({
  datumIdx: index('dispatch_data_datum_idx').on(table.datum)
}));

// ARiL table
export const ariL = sqliteTable('ARiL', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: integer('datum', { mode: 'timestamp' }),
  waTaPositionen: integer('waTaPositionen'),
  umlagerungen: integer('Umlagerungen'),
  belegtePlaetze: integer('belegtePlaetze'),
  systemtablareRuecksackStk: integer('systemtablareRuecksackStk'),
  systemtablareGesamtStk: integer('systemtablareGesamtStk'),
  systemtablareEinzelBelegt: integer('systemtablareEinzelBelegt'),
  belegtRinge: integer('belegtRinge'),
  auslastung: real('Auslastung'),
  alleBewegungen: integer('alleBewegungen'),
  cuttingLagerKunde: integer('cuttingLagerKunde'),
  cuttingLagerRest: integer('cuttingLagerRest'),
  lagerCutting: integer('lagerCutting')
}, (table) => ({
  datumIdx: index('ARiL_datum_idx').on(table.datum)
}));

// ATrL table
export const atrL = sqliteTable('ATrL', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: integer('datum', { mode: 'timestamp' }),
  umlagerungen: integer('umlagerungen'),
  waTaPositionen: integer('waTaPositionen'),
  belegtePlaetze: integer('belegtePlaetze'),
  davonSystempaletten: integer('davonSystempaletten'),
  systempalettenstapelRucksackpaetzen: integer('SystempalettenstapelRucksackpaetzen'),
  systempalettenstapelEinzel: integer('SystempalettenstapelEinzel'),
  plaetzeSystempalettenstapelEinzel: integer('PlaetzeSystempalettenstapelEinzel'),
  plaetzeMitTrommelBelegt: integer('plaetzeMitTrommelBelegt'),
  auslastung: real('Auslastung'),
  bewegungen: integer('Bewegungen'),
  einlagerungAblKunde: integer('EinlagerungAblKunde'),
  einlagerungAblRest: integer('EinlagerungAblRest'),
  auslagerungAbl: integer('AuslagerungAbl'),
  weAtrl: integer('weAtrl')
}, (table) => ({
  datumIdx: index('ATrL_datum_idx').on(table.datum)
}));

// Ablaengerei table
export const ablaengerei = sqliteTable('Ablaengerei', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: integer('datum', { mode: 'timestamp' }),
  cutLagerK220: integer('cutLagerK220').default(0),
  cutLagerR220: integer('cutLagerR220').default(0),
  lagerCut220: integer('lagerCut220').default(0),
  cutLagerK240: integer('cutLagerK240').default(0),
  cutLagerR240: integer('cutLagerR240').default(0),
  lagerCut240: integer('lagerCut240').default(0),
  cutTt: integer('cutTT').default(0),
  cutTr: integer('cutTR').default(0),
  cutRr: integer('cutRR').default(0),
  cutGesamt: integer('cutGesamt').default(0),
  pickCut: integer('pickCut').default(0),
  cutLager200: integer('cutLager200').default(0),
  cutLagerK200: integer('cutLagerK200').default(0),
  lagerCut200: integer('lagerCut200').default(0)
}, (table) => ({
  datumIdx: index('Ablaengerei_datum_idx').on(table.datum)
}));

// System table
export const system = sqliteTable('System', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: integer('datum', { mode: 'timestamp' }),
  verfuegbarkeitFts: real('verfuegbarkeitFTS')
}, (table) => ({
  datumIdx: index('System_datum_idx').on(table.datum)
}));

// WE table
export const we = sqliteTable('WE', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: integer('datum', { mode: 'timestamp' }),
  wareneingang: integer('Wareneingang')
}, (table) => ({
  datumIdx: index('WE_datum_idx').on(table.datum)
}));

// Auslastung200 table
export const auslastung200 = sqliteTable('Auslastung200', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  aufnahmeDatum: integer('aufnahmeDatum', { mode: 'timestamp' }),
  auslastungA: real('auslastungA'),
  auslastungB: real('auslastungB'),
  auslastungC: real('auslastungC')
}, (table) => ({
  aufnahmeDatumIdx: index('Auslastung200_aufnahmeDatum_idx').on(table.aufnahmeDatum)
}));

// Auslastung240 table
export const auslastung240 = sqliteTable('Auslastung240', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  aufnahmeDatum: integer('aufnahmeDatum', { mode: 'timestamp' }),
  auslastungA: real('auslastungA'),
  auslastungB: real('auslastungB'),
  auslastungC: real('auslastungC')
}, (table) => ({
  aufnahmeDatumIdx: index('Auslastung240_aufnahmeDatum_idx').on(table.aufnahmeDatum)
}));

// Maschinen table
export const maschinen = sqliteTable('maschinen', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  machine: text('Machine').unique(),
  schnitteProStd: real('schnitteProStd')
});

// Schnitte table
export const schnitte = sqliteTable('schnitte', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  datum: text('Datum'),
  m5RH1: integer('M5-R-H1'),
  m6TH1: integer('M6-T-H1'),
  m7RH1: integer('M7-R-H1'),
  m8TH1: integer('M8-T-H1'),
  m9RH1: integer('M9-R-H1'),
  m10TH1: integer('M10-T-H1'),
  m11RH1: integer('M11-R-H1'),
  m12TH1: integer('M12-T-H1'),
  m13RH1: integer('M13-R-H1'),
  m14TH1: integer('M14-T-H1'),
  m15RH1: integer('M15-R-H1')
});

// Materialdaten table
export const materialdaten = sqliteTable('materialdaten', {
  matnr: text('matnr').primaryKey(),
  materialkurztext: text('materialkurztext'),
  kabeldurchmesser: real('kabeldurchmesser'),
  zuschlagKabeldurchmesser: real('zuschlagKabeldurchmesser'),
  biegefaktor: real('biegefaktor'),
  kleinsterErlauberFreiraum: real('kleinsterErlauberFreiraum'),
  bruttogewicht: real('bruttogewicht'),
  created_at: text('created_at'),
  updated_at: text('updated_at')
});

// Trommeldaten table
export const trommeldaten = sqliteTable('trommeldaten', {
  trommeldaten: text('trommeldaten').primaryKey(),
  aussendurchmesser: real('aussendurchmesser'),
  kerndurchmesser: real('kerndurchmesser')
});

// Stoerungen table
export const stoerungen = sqliteTable('Stoerungen', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  title: text('title').notNull(),
  description: text('description'),
  severity: text('severity').notNull(),
  status: text('status').notNull(),
  category: text('category'),
  assignedTo: text('assignedTo'),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull(),
  resolvedAt: text('resolvedAt'),
  estimatedResolution: text('estimatedResolution'),
  actualResolution: text('actualResolution'),
  impact: text('impact'),
  urgency: text('urgency'),
  priority: text('priority'),
  tags: text('tags'),
  relatedSystems: text('relatedSystems'),
  rootCause: text('rootCause'),
  preventiveMeasures: text('preventiveMeasures'),
  customerImpact: text('customerImpact'),
  businessImpact: text('businessImpact'),
  communicationPlan: text('communicationPlan'),
  escalationLevel: integer('escalationLevel').default(0),
  isRecurring: integer('isRecurring').default(0),
  recurringPattern: text('recurringPattern'),
  lastOccurrence: text('lastOccurrence'),
  nextReview: text('nextReview'),
  attachments: text('attachments'),
  externalTicketId: text('externalTicketId'),
  changeRequestId: text('changeRequestId'),
  knowledgeBaseId: text('knowledgeBaseId'),
  slaDeadline: text('slaDeadline'),
  slaStatus: text('slaStatus'),
  resolutionSteps: text('resolutionSteps'),
  testingRequired: integer('testingRequired').default(0),
  testingCompleted: integer('testingCompleted').default(0),
  approvalRequired: integer('approvalRequired').default(0),
  approvalReceived: integer('approvalReceived').default(0),
  deploymentRequired: integer('deploymentRequired').default(0),
  deploymentCompleted: integer('deploymentCompleted').default(0),
  monitoringRequired: integer('monitoringRequired').default(0),
  monitoringSetup: integer('monitoringSetup').default(0),
  postIncidentReview: integer('postIncidentReview').default(0),
  lessonsLearned: text('lessonsLearned'),
  improvementActions: text('improvementActions'),
  costImpact: real('costImpact'),
  timeToDetection: integer('timeToDetection'),
  timeToResolution: integer('timeToResolution'),
  customerSatisfaction: integer('customerSatisfaction'),
  teamSatisfaction: integer('teamSatisfaction'),
  processEfficiency: integer('processEfficiency'),
  toolsUsed: text('toolsUsed'),
  skillsRequired: text('skillsRequired'),
  trainingNeeded: text('trainingNeeded'),
  vendorInvolved: text('vendorInvolved'),
  vendorResponse: text('vendorResponse'),
  regulatoryImpact: text('regulatoryImpact'),
  complianceIssues: text('complianceIssues'),
  auditTrail: text('auditTrail'),
  dataBackup: integer('dataBackup').default(0),
  dataRecovery: integer('dataRecovery').default(0),
  securityImpact: text('securityImpact'),
  privacyImpact: text('privacyImpact'),
  environmentalImpact: text('environmentalImpact'),
  sustainabilityImpact: text('sustainabilityImpact')
});

// StoerungsComments table
export const stoerungsComments = sqliteTable('StoerungsComments', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  stoerungId: integer('stoerungId').notNull(),
  author: text('author').notNull(),
  content: text('content').notNull(),
  timestamp: text('timestamp').notNull(),
  isInternal: integer('isInternal').default(0),
  attachments: text('attachments')
});

// SystemStatus table
export const systemStatus = sqliteTable('SystemStatus', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  systemName: text('systemName').notNull().unique(),
  status: text('status').notNull(),
  lastUpdated: text('lastUpdated').notNull(),
  description: text('description'),
  category: text('category'),
  priority: text('priority'),
  owner: text('owner'),
  location: text('location'),
  ipAddress: text('ipAddress'),
  port: integer('port'),
  protocol: text('protocol'),
  healthCheckUrl: text('healthCheckUrl'),
  monitoringEnabled: integer('monitoringEnabled').default(1),
  alertsEnabled: integer('alertsEnabled').default(1),
  maintenanceMode: integer('maintenanceMode').default(0),
  expectedUptime: real('expectedUptime'),
  actualUptime: real('actualUptime'),
  lastDowntime: text('lastDowntime'),
  downtimeReason: text('downtimeReason'),
  recoveryTime: integer('recoveryTime'),
  dependencies: text('dependencies'),
  tags: text('tags'),
  version: text('version'),
  environment: text('environment'),
  region: text('region'),
  dataCenter: text('dataCenter'),
  vendor: text('vendor'),
  supportContact: text('supportContact'),
  documentationUrl: text('documentationUrl'),
  runbookUrl: text('runbookUrl'),
  dashboardUrl: text('dashboardUrl'),
  logUrl: text('logUrl'),
  metricUrl: text('metricUrl'),
  alertUrl: text('alertUrl'),
  backupStatus: text('backupStatus'),
  backupLastRun: text('backupLastRun'),
  securityStatus: text('securityStatus'),
  complianceStatus: text('complianceStatus'),
  performanceMetrics: text('performanceMetrics'),
  capacityMetrics: text('capacityMetrics'),
  costMetrics: text('costMetrics'),
  slaTarget: real('slaTarget'),
  slaActual: real('slaActual'),
  incidentCount: integer('incidentCount').default(0),
  changeCount: integer('changeCount').default(0),
  deploymentCount: integer('deploymentCount').default(0),
  configurationItems: text('configurationItems'),
  businessService: text('businessService'),
  businessImpact: text('businessImpact'),
  riskLevel: text('riskLevel'),
  automationLevel: text('automationLevel'),
  scalabilityRating: integer('scalabilityRating'),
  reliabilityRating: integer('reliabilityRating'),
  securityRating: integer('securityRating'),
  performanceRating: integer('performanceRating'),
  usabilityRating: integer('usabilityRating'),
  maintainabilityRating: integer('maintainabilityRating'),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull()
});

// SystemStatusMessage table
export const systemStatusMessage = sqliteTable('SystemStatusMessage', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  systemId: integer('systemId').notNull(),
  message: text('message').notNull(),
  messageType: text('messageType').notNull(),
  timestamp: text('timestamp').notNull(),
  author: text('author'),
  isActive: integer('isActive').default(1),
  priority: text('priority'),
  audience: text('audience'),
  expiresAt: text('expiresAt'),
  tags: text('tags')
});

// User table
export const user = sqliteTable('User', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  username: text('username').notNull().unique(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  role: text('role').notNull().default('user'),
  isActive: integer('isActive').default(1),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull(),
  lastLogin: text('lastLogin'),
  loginAttempts: integer('loginAttempts').default(0),
  lockedUntil: text('lockedUntil'),
  resetToken: text('resetToken'),
  resetTokenExpires: text('resetTokenExpires'),
  emailVerified: integer('emailVerified').default(0),
  emailVerificationToken: text('emailVerificationToken'),
  twoFactorEnabled: integer('twoFactorEnabled').default(0),
  twoFactorSecret: text('twoFactorSecret'),
  preferences: text('preferences'),
  avatar: text('avatar'),
  firstName: text('firstName'),
  lastName: text('lastName'),
  phone: text('phone'),
  department: text('department'),
  position: text('position'),
  manager: text('manager'),
  location: text('location'),
  timezone: text('timezone'),
  language: text('language').default('de'),
  theme: text('theme').default('light'),
  notifications: text('notifications')
});

// Bereitschafts tables
export const bereitschaftsPersonen = sqliteTable('BereitschaftsPersonen', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull(),
  email: text('email').notNull(),
  telefon: text('telefon'),
  abteilung: text('abteilung'),
  rolle: text('rolle'),
  aktiv: integer('aktiv').default(1),
  reihenfolge: integer('reihenfolge').default(0),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull()
});

export const bereitschaftsWochen = sqliteTable('BereitschaftsWochen', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  personId: integer('personId').notNull(),
  kalenderwoche: integer('kalenderwoche').notNull(),
  jahr: integer('jahr').notNull(),
  typ: text('typ').notNull(),
  wochenStart: integer('wochenStart').notNull(),
  wochenEnde: integer('wochenEnde').notNull(),
  von: integer('von').notNull(),
  bis: integer('bis').notNull(),
  aktiv: integer('aktiv').default(1),
  notiz: text('notiz'),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull()
});

export const bereitschaftsAusnahmen = sqliteTable('BereitschaftsAusnahmen', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  personId: integer('personId').notNull(),
  von: integer('von').notNull(),
  bis: integer('bis').notNull(),
  grund: text('grund').notNull(),
  ersatzPersonId: integer('ersatzPersonId'),
  aktiv: integer('aktiv').default(1),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull()
});

export const bereitschaftsKonfiguration = sqliteTable('BereitschaftsKonfiguration', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  wechselTag: integer('wechselTag').default(5),
  wechselUhrzeit: text('wechselUhrzeit').default('08:00'),
  rotationAktiv: integer('rotationAktiv').default(1),
  benachrichtigungTage: integer('benachrichtigungTage').default(2),
  emailBenachrichtigung: integer('emailBenachrichtigung').default(1),
  createdAt: text('createdAt').notNull(),
  updatedAt: text('updatedAt').notNull()
});