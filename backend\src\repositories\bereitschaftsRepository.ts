import { db } from '../db';
import { bereitschaftsPersonen, bereitschaftsWochen, bereitschaftsAusnahmen, bereitschaftsKonfiguration } from '../db/schema';
import { eq, and, or, desc, asc, gte, lte, lt, gt } from 'drizzle-orm';
import { startOfWeek, addWeeks, addDays, format } from 'date-fns';
import { de } from 'date-fns/locale';

export interface BereitschaftsPersonData {
  id?: number;
  name: string;
  telefon: string;
  email: string;
  abteilung: string;
  aktiv?: boolean;
  reihenfolge?: number;
}

export interface BereitschaftsWocheData {
  id?: number;
  personId: number;
  wochenStart: Date;
  wochenEnde: Date;
  von: Date;
  bis: Date;
  aktiv?: boolean;
  notiz?: string;
}

export interface BereitschaftsAusnahmeData {
  id?: number;
  personId: number;
  von: Date;
  bis: Date;
  grund: string;
  ersatzPersonId?: number;
  aktiv?: boolean;
}

export interface BereitschaftsKonfigurationData {
  id?: number;
  wechselTag?: number;
  wechselUhrzeit?: string;
  rotationAktiv?: boolean;
  benachrichtigungTage?: number;
  emailBenachrichtigung?: boolean;
}

export class BereitschaftsRepository {
  private db = db;

  // Personen verwalten
  async getAllPersonen() {
    const personen = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.aktiv, 1))
      .orderBy(asc(bereitschaftsPersonen.reihenfolge));

    // Fetch bereitschaftsWochen for each person separately
    const personenWithWochen = await Promise.all(
      personen.map(async (person) => {
        const wochen = await this.db.select().from(bereitschaftsWochen)
          .where(and(
            eq(bereitschaftsWochen.personId, person.id),
            eq(bereitschaftsWochen.aktiv, 1)
          ))
          .orderBy(desc(bereitschaftsWochen.wochenStart))
          .limit(5);
        
        return { ...person, bereitschaftsWochen: wochen };
      })
    );

    return personenWithWochen;
  }

  async getPersonById(id: number) {
    const result = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, id))
      .limit(1);
    
    const person = result[0];
    if (!person) return null;

    const [wochen, ausnahmen] = await Promise.all([
      this.db.select().from(bereitschaftsWochen)
        .where(and(
          eq(bereitschaftsWochen.personId, id),
          eq(bereitschaftsWochen.aktiv, 1)
        ))
        .orderBy(desc(bereitschaftsWochen.wochenStart)),
      
      this.db.select().from(bereitschaftsAusnahmen)
        .where(and(
          eq(bereitschaftsAusnahmen.personId, id),
          eq(bereitschaftsAusnahmen.aktiv, 1)
        ))
        .orderBy(desc(bereitschaftsAusnahmen.von))
    ]);

    return {
      ...person,
      bereitschaftsWochen: wochen,
      bereitschaftsAusnahmen: ausnahmen
    };
  }

  async createPerson(data: BereitschaftsPersonData) {
    // Get max reihenfolge
    const maxResult = await this.db.select({ 
      maxReihenfolge: bereitschaftsPersonen.reihenfolge 
    })
    .from(bereitschaftsPersonen)
    .orderBy(desc(bereitschaftsPersonen.reihenfolge))
    .limit(1);

    const maxReihenfolge = maxResult[0]?.maxReihenfolge ?? 0;

    const result = await this.db.insert(bereitschaftsPersonen)
      .values({
        ...data,
        reihenfolge: data.reihenfolge ?? maxReihenfolge + 1
      })
      .returning();

    return result[0];
  }

  async updatePerson(id: number, data: Partial<BereitschaftsPersonData>) {
    const result = await this.db.update(bereitschaftsPersonen)
      .set(data)
      .where(eq(bereitschaftsPersonen.id, id))
      .returning();
    
    return result[0];
  }

  async deletePerson(id: number) {
    const result = await this.db.update(bereitschaftsPersonen)
      .set({ aktiv: 0 })
      .where(eq(bereitschaftsPersonen.id, id))
      .returning();
    
    return result[0];
  }

  async updatePersonenReihenfolge(personenIds: number[]) {
    const updates = personenIds.map(async (id, index) =>
      this.db.update(bereitschaftsPersonen)
        .set({ reihenfolge: index + 1 })
        .where(eq(bereitschaftsPersonen.id, id))
        .returning()
    );

    return await Promise.all(updates);
  }

  // Wochen verwalten
  async getWochenPlan(startDate: Date, anzahlWochen: number) {
    const endDate = addWeeks(startDate, anzahlWochen);
    const startTimestamp = startDate.getTime();
    const endTimestamp = endDate.getTime();
    
    const wochen = await this.db.select().from(bereitschaftsWochen)
      .where(and(
        eq(bereitschaftsWochen.aktiv, 1),
        gte(bereitschaftsWochen.wochenStart, startTimestamp),
        lt(bereitschaftsWochen.wochenStart, endTimestamp)
      ))
      .orderBy(asc(bereitschaftsWochen.wochenStart));

    // Fetch person data for each week
    const wochenWithPerson = await Promise.all(
      wochen.map(async (woche) => {
        const person = await this.db.select().from(bereitschaftsPersonen)
          .where(eq(bereitschaftsPersonen.id, woche.personId))
          .limit(1);
        
        return { ...woche, person: person[0] || null };
      })
    );

    return wochenWithPerson;
  }

  async getAktuelleBereitschaft() {
    const heute = Date.now();
    
    const result = await this.db.select().from(bereitschaftsWochen)
      .where(and(
        eq(bereitschaftsWochen.aktiv, 1),
        lte(bereitschaftsWochen.von, heute),
        gt(bereitschaftsWochen.bis, heute)
      ))
      .limit(1);

    const woche = result[0];
    if (!woche) return null;

    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, woche.personId))
      .limit(1);

    return { ...woche, person: person[0] || null };
  }

  async createWoche(data: BereitschaftsWocheData) {
    // Convert Date objects to timestamps for database storage
    const wocheData = {
      ...data,
      wochenStart: data.wochenStart.getTime(),
      wochenEnde: data.wochenEnde.getTime(),
      von: data.von.getTime(),
      bis: data.bis.getTime()
    };

    const result = await this.db.insert(bereitschaftsWochen)
      .values(wocheData)
      .returning();

    const woche = result[0];
    
    // Fetch person data
    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, woche.personId))
      .limit(1);

    return { ...woche, person: person[0] || null };
  }

  async updateWoche(id: number, data: Partial<BereitschaftsWocheData>) {
    // Convert Date objects to timestamps if they exist
    const updateData: any = { ...data };
    if (data.wochenStart) updateData.wochenStart = data.wochenStart.getTime();
    if (data.wochenEnde) updateData.wochenEnde = data.wochenEnde.getTime();
    if (data.von) updateData.von = data.von.getTime();
    if (data.bis) updateData.bis = data.bis.getTime();

    const result = await this.db.update(bereitschaftsWochen)
      .set(updateData)
      .where(eq(bereitschaftsWochen.id, id))
      .returning();

    const woche = result[0];
    
    // Fetch person data
    const person = await this.db.select().from(bereitschaftsPersonen)
      .where(eq(bereitschaftsPersonen.id, woche.personId))
      .limit(1);

    return { ...woche, person: person[0] || null };
  }

  async deleteWoche(id: number) {
    const result = await this.db.update(bereitschaftsWochen)
      .set({ aktiv: 0 })
      .where(eq(bereitschaftsWochen.id, id))
      .returning();
    
    return result[0];
  }

  // Automatische Wochenplanung generieren
  async generiereWochenplan(startDate: Date, anzahlWochen: number) {
    const personen = await this.getAllPersonen();
    if (personen.length === 0) {
      throw new Error('Keine aktiven Bereitschaftspersonen gefunden');
    }

    const konfiguration = await this.getKonfiguration();
    const wechselTag = konfiguration?.wechselTag ?? 5; // Freitag
    const wechselUhrzeit = konfiguration?.wechselUhrzeit ?? '08:00';

    const wochen: BereitschaftsWocheData[] = [];

    for (let i = 0; i < anzahlWochen; i++) {
      const wochenStart = addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), i);
      const wochenEnde = addWeeks(wochenStart, 1);
      
      // Berechne Freitag der Woche
      const freitag = addDays(wochenStart, wechselTag - 1);
      const naechsterFreitag = addDays(freitag, 7);

      // Rotiere durch die Personen
      const personIndex = i % personen.length;
      const person = personen[personIndex];

      wochen.push({
        personId: person.id,
        wochenStart,
        wochenEnde,
        von: freitag,
        bis: naechsterFreitag,
        aktiv: true
      });
    }

    // Lösche bestehende Wochen im Zeitraum
    await prisma.bereitschaftsWoche.updateMany({
      where: {
        wochenStart: {
          gte: startOfWeek(startDate, { weekStartsOn: 1 }),
          lt: addWeeks(startOfWeek(startDate, { weekStartsOn: 1 }), anzahlWochen)
        }
      },
      data: { aktiv: false }
    });

    // Erstelle neue Wochen
    const erstellteWochen = await Promise.all(
      wochen.map(woche => this.createWoche(woche))
    );

    return erstellteWochen;
  }

  // Ausnahmen verwalten
  async getAllAusnahmen() {
    return await prisma.bereitschaftsAusnahme.findMany({
      where: { aktiv: true },
      include: {
        person: true
      },
      orderBy: { von: 'desc' }
    });
  }

  async createAusnahme(data: BereitschaftsAusnahmeData) {
    return await prisma.bereitschaftsAusnahme.create({
      data,
      include: {
        person: true
      }
    });
  }

  async updateAusnahme(id: number, data: Partial<BereitschaftsAusnahmeData>) {
    return await prisma.bereitschaftsAusnahme.update({
      where: { id },
      data,
      include: {
        person: true
      }
    });
  }

  async deleteAusnahme(id: number) {
    return await prisma.bereitschaftsAusnahme.update({
      where: { id },
      data: { aktiv: false }
    });
  }

  // Konfiguration verwalten
  async getKonfiguration() {
    const config = await prisma.bereitschaftsKonfiguration.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    // Standardkonfiguration falls keine existiert
    if (!config) {
      return await this.createKonfiguration({});
    }

    // Map database fields to expected frontend fields
    return {
      id: config.id,
      wechselTag: config.wechsel_tag,
      wechselUhrzeit: config.wechselUhrzeit,
      rotationAktiv: config.rotationAktiv,
      benachrichtigungTage: config.benachrichtigungTage,
      emailBenachrichtigung: config.emailBenachrichtigung,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString()
    };
  }

  async createKonfiguration(data: BereitschaftsKonfigurationData) {
    const created = await prisma.bereitschaftsKonfiguration.create({
      data: {
        wechsel_tag: data.wechselTag ?? 5,
        wechselUhrzeit: data.wechselUhrzeit ?? '08:00',
        rotationAktiv: data.rotationAktiv ?? true,
        benachrichtigungTage: data.benachrichtigungTage ?? 2,
        emailBenachrichtigung: data.emailBenachrichtigung ?? true
      }
    });

    // Return mapped result
    return {
      id: created.id,
      wechselTag: created.wechsel_tag,
      wechselUhrzeit: created.wechselUhrzeit,
      rotationAktiv: created.rotationAktiv,
      benachrichtigungTage: created.benachrichtigungTage,
      emailBenachrichtigung: created.emailBenachrichtigung,
      createdAt: created.createdAt.toISOString(),
      updatedAt: created.updatedAt.toISOString()
    };
  }

  async updateKonfiguration(data: Partial<BereitschaftsKonfigurationData>) {
    const existingConfig = await prisma.bereitschaftsKonfiguration.findFirst({
      orderBy: { createdAt: 'desc' }
    });
    
    if (!existingConfig) {
      throw new Error('Keine Konfiguration gefunden');
    }

    // Map frontend fields to database fields
    const updateData: any = {};
    if (data.wechselTag !== undefined) updateData.wechsel_tag = data.wechselTag;
    if (data.wechselUhrzeit !== undefined) updateData.wechselUhrzeit = data.wechselUhrzeit;
    if (data.rotationAktiv !== undefined) updateData.rotationAktiv = data.rotationAktiv;
    if (data.benachrichtigungTage !== undefined) updateData.benachrichtigungTage = data.benachrichtigungTage;
    if (data.emailBenachrichtigung !== undefined) updateData.emailBenachrichtigung = data.emailBenachrichtigung;

    const updated = await prisma.bereitschaftsKonfiguration.update({
      where: { id: existingConfig.id },
      data: updateData
    });

    // Return mapped result
    return {
      id: updated.id,
      wechselTag: updated.wechsel_tag,
      wechselUhrzeit: updated.wechselUhrzeit,
      rotationAktiv: updated.rotationAktiv,
      benachrichtigungTage: updated.benachrichtigungTage,
      emailBenachrichtigung: updated.emailBenachrichtigung,
      createdAt: updated.createdAt.toISOString(),
      updatedAt: updated.updatedAt.toISOString()
    };
  }

  // Hilfsmethoden
  async getPersonenInZeitraum(von: Date, bis: Date) {
    return await prisma.bereitschaftsWoche.findMany({
      where: {
        aktiv: true,
        OR: [
          {
            von: { lte: bis },
            bis: { gte: von }
          }
        ]
      },
      include: {
        person: true
      },
      orderBy: { von: 'asc' }
    });
  }

  async validateWochenplan(wochen: BereitschaftsWocheData[]) {
    const errors: string[] = [];

    // Prüfe auf Überschneidungen
    for (let i = 0; i < wochen.length; i++) {
      for (let j = i + 1; j < wochen.length; j++) {
        const woche1 = wochen[i];
        const woche2 = wochen[j];

        if (woche1.von < woche2.bis && woche1.bis > woche2.von) {
          errors.push(`Überschneidung zwischen Woche ${i + 1} und ${j + 1}`);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const bereitschaftsRepository = new BereitschaftsRepository();