import { db } from '../db';
import { user } from '../db/schema';
import { eq, or } from 'drizzle-orm';

type User = typeof user.$inferSelect;

export class UserRepository {
  private db = db;

  constructor() {
    // Drizzle DB wird direkt importiert
  }

  async findByEmailOrUsername(email: string, username: string): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(or(
        eq(user.email, email),
        eq(user.username, username)
      ))
      .limit(1);
    return result[0] || null;
  }

  async findByEmail(email: string): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(eq(user.email, email))
      .limit(1);
    return result[0] || null;
  }

  async findByUsername(username: string): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(eq(user.username, username))
      .limit(1);
    return result[0] || null;
  }

  async createUser(userData: {
    email: string;
    username: string;
    name?: string;
    passwordHash: string;
  }): Promise<User> {
    const result = await this.db.insert(user).values({
      email: userData.email,
      username: userData.username,
      name: userData.name,
      passwordHash: userData.passwordHash,
      updatedAt: new Date().toISOString()
    }).returning();
    return result[0];
  }

  async findById(id: number): Promise<User | null> {
    const result = await this.db.select().from(user)
      .where(eq(user.id, id))
      .limit(1);
    return result[0] || null;
  }
}